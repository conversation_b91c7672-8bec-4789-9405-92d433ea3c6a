/**
 * مدير الأدوات المالية
 */

const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

class InstrumentsManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.connection = null;
        this.instruments = [];
        this.lastUpdate = null;
        this.dataPath = config.get('data.dataPath') || './data';
    }

    /**
     * تهيئة مدير الأدوات المالية
     */
    async initialize(connection) {
        try {
            this.connection = connection;
            
            // إنشاء مجلد البيانات
            await this.ensureDataDirectory();
            
            // تحميل البيانات المحفوظة
            await this.loadStoredData();
            
            // جلب الأدوات المالية من المنصة
            await this.fetchInstruments();
            
            this.logger.info('✅ تم تهيئة مدير الأدوات المالية بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تهيئة مدير الأدوات المالية:', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        try {
            await fs.mkdir(this.dataPath, { recursive: true });
        } catch (error) {
            // تجاهل الخطأ إذا كان المجلد موجوداً
        }
    }

    /**
     * تحميل البيانات المحفوظة
     */
    async loadStoredData() {
        try {
            const instrumentsFile = path.join(this.dataPath, 'instruments.json');
            const data = await fs.readFile(instrumentsFile, 'utf8');
            const instrumentsData = JSON.parse(data);
            
            this.instruments = instrumentsData.instruments || [];
            this.lastUpdate = instrumentsData.lastUpdate;
            
            this.logger.info(`📥 تم تحميل ${this.instruments.length} أداة مالية محفوظة`);
            
        } catch (error) {
            this.logger.info('📝 لا توجد أدوات مالية محفوظة، سيتم جلبها من المنصة');
            this.instruments = [];
        }
    }

    /**
     * جلب الأدوات المالية من المنصة
     */
    async fetchInstruments() {
        if (!this.connection || !this.connection.isConnected()) {
            throw new Error('لا يوجد اتصال نشط');
        }

        try {
            this.logger.info('📊 جلب الأدوات المالية من المنصة...');
            
            // انتظار تحميل البيانات
            await this.connection.page.waitForTimeout(5000);
            
            const instruments = await this.connection.evaluate(() => {
                const extractedInstruments = [];

                console.log('Starting instrument search...');

                // البحث عن قائمة الأدوات في واجهة المستخدم - Quotex specific
                const assetSelectors = [
                    // Quotex specific selectors
                    '.assets-list',
                    '.asset-item',
                    '.instrument-item',
                    '.currency-pair',
                    '.trading-instrument',
                    '[data-asset]',
                    '[data-symbol]',
                    '.asset-list',
                    '.instruments-list',
                    '.currency-list',
                    '.trading-assets',
                    '[data-assets]',
                    '.assets-dropdown',
                    '.asset-selector',
                    '.symbol-list'
                ];

                // البحث في العناصر المحددة
                for (const selector of assetSelectors) {
                    const containers = document.querySelectorAll(selector);
                    console.log(`Checking selector: ${selector}, found ${containers.length} containers`);

                    containers.forEach(container => {
                        const items = container.querySelectorAll('div, li, option, span, button');
                        console.log(`Found ${items.length} items in container`);

                        items.forEach(item => {
                            const text = item.textContent || item.innerText;
                            const dataSymbol = item.getAttribute('data-symbol') || item.getAttribute('data-asset');

                            if (dataSymbol) {
                                console.log(`Found data-symbol: ${dataSymbol}`);
                                extractedInstruments.push({
                                    symbol: dataSymbol.toUpperCase(),
                                    name: text.trim() || dataSymbol,
                                    type: 'currency', // will be detected later
                                    isActive: !item.classList.contains('disabled'),
                                    source: 'data-attribute'
                                });
                            } else if (text && text.length < 20) {
                                // البحث عن أنماط الرموز المختلفة
                                const patterns = [
                                    /([A-Z]{3}\/[A-Z]{3})/g,
                                    /([A-Z]{3}USD)/g,
                                    /(BTC[A-Z]*)/g,
                                    /(ETH[A-Z]*)/g,
                                    /(EUR[A-Z]*)/g,
                                    /(GBP[A-Z]*)/g,
                                    /(USD[A-Z]{3})/g
                                ];

                                for (const pattern of patterns) {
                                    const matches = text.match(pattern);
                                    if (matches) {
                                        matches.forEach(symbol => {
                                            console.log(`Found symbol pattern: ${symbol}`);
                                            extractedInstruments.push({
                                                symbol: symbol,
                                                name: text.trim(),
                                                type: 'currency', // will be detected later
                                                isActive: !item.classList.contains('disabled'),
                                                source: 'text-pattern'
                                            });
                                        });
                                    }
                                }
                            }
                        });
                    });
                }

                // البحث في جميع العناصر إذا لم نجد شيئاً
                if (extractedInstruments.length === 0) {
                    console.log('No instruments found in specific selectors, searching all elements...');
                    const allElements = document.querySelectorAll('*');

                    for (const element of allElements) {
                        const text = element.textContent || element.innerText;
                        if (text && text.length < 15) {
                            const symbolMatch = text.match(/^([A-Z]{3}\/[A-Z]{3}|[A-Z]{3}USD|BTC|ETH|EUR|GBP)$/);
                            if (symbolMatch) {
                                console.log(`Found symbol in general search: ${symbolMatch[1]}`);
                                extractedInstruments.push({
                                    symbol: symbolMatch[1],
                                    name: text.trim(),
                                    type: 'currency',
                                    isActive: true,
                                    source: 'general-search'
                                });
                            }
                        }
                    }
                }

                // البحث في localStorage عن بيانات الأدوات
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    const value = localStorage.getItem(key);
                    
                    if (key.includes('instrument') || key.includes('asset') || key.includes('symbol')) {
                        try {
                            const data = JSON.parse(value);
                            if (Array.isArray(data)) {
                                data.forEach(item => {
                                    if (item.symbol || item.name) {
                                        extractedInstruments.push({
                                            symbol: item.symbol || item.name,
                                            name: item.name || item.symbol,
                                            type: item.type || this.detectInstrumentType(item.symbol),
                                            isActive: item.isActive !== false,
                                            profitPercentage: item.profitPercentage || item.profit,
                                            source: 'localStorage'
                                        });
                                    }
                                });
                            }
                        } catch (e) {
                            // تجاهل أخطاء التحليل
                        }
                    }
                }

                // إضافة الأدوات الشائعة كاحتياطي
                const commonInstruments = [
                    { symbol: 'EURUSD', name: 'EUR/USD', type: 'currency' },
                    { symbol: 'GBPUSD', name: 'GBP/USD', type: 'currency' },
                    { symbol: 'USDJPY', name: 'USD/JPY', type: 'currency' },
                    { symbol: 'AUDUSD', name: 'AUD/USD', type: 'currency' },
                    { symbol: 'USDCAD', name: 'USD/CAD', type: 'currency' },
                    { symbol: 'USDCHF', name: 'USD/CHF', type: 'currency' },
                    { symbol: 'BTCUSD', name: 'Bitcoin', type: 'cryptocurrency' },
                    { symbol: 'ETHUSD', name: 'Ethereum', type: 'cryptocurrency' },
                    { symbol: 'LTCUSD', name: 'Litecoin', type: 'cryptocurrency' },
                    { symbol: 'ADAUSD', name: 'Cardano', type: 'cryptocurrency' },
                    { symbol: 'DOGUSD', name: 'Dogecoin', type: 'cryptocurrency' }
                ];

                commonInstruments.forEach(inst => {
                    if (!extractedInstruments.find(e => e.symbol === inst.symbol)) {
                        extractedInstruments.push({
                            ...inst,
                            isActive: true,
                            source: 'default'
                        });
                    }
                });

                return extractedInstruments;
            });

            // معالجة وتنظيف البيانات
            this.instruments = this.processInstruments(instruments);
            this.lastUpdate = new Date().toISOString();
            
            // حفظ البيانات
            await this.saveData();
            
            this.emit('instrumentsUpdate', this.instruments);
            this.logger.info(`✅ تم جلب ${this.instruments.length} أداة مالية`);
            
        } catch (error) {
            this.logger.error('فشل في جلب الأدوات المالية:', error);
            throw error;
        }
    }

    /**
     * معالجة وتنظيف بيانات الأدوات
     */
    processInstruments(rawInstruments) {
        const processed = [];
        const seen = new Set();

        rawInstruments.forEach(instrument => {
            // تجنب التكرار
            if (seen.has(instrument.symbol)) {
                return;
            }
            seen.add(instrument.symbol);

            // تنظيف البيانات
            const cleanInstrument = {
                symbol: instrument.symbol.toUpperCase(),
                name: instrument.name || instrument.symbol,
                type: instrument.type || this.detectInstrumentType(instrument.symbol),
                isActive: instrument.isActive !== false,
                profitPercentage: instrument.profitPercentage || this.getDefaultProfitPercentage(instrument.type),
                minAmount: 1,
                maxAmount: 1000,
                expirationTimes: this.getDefaultExpirationTimes(),
                source: instrument.source || 'platform',
                lastUpdate: new Date().toISOString()
            };

            processed.push(cleanInstrument);
        });

        return processed.sort((a, b) => a.symbol.localeCompare(b.symbol));
    }

    /**
     * تحديد نوع الأداة المالية
     */
    detectInstrumentType(symbol) {
        if (!symbol) return 'unknown';
        
        const crypto = ['BTC', 'ETH', 'LTC', 'ADA', 'DOG', 'XRP', 'BNB', 'SOL', 'AVAX', 'DOT'];
        const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'NZD'];
        
        if (crypto.some(c => symbol.includes(c))) {
            return 'cryptocurrency';
        }
        
        if (currencies.some(c => symbol.includes(c)) && symbol.includes('/')) {
            return 'currency';
        }
        
        if (symbol.length <= 5 && !symbol.includes('/')) {
            return 'stock';
        }
        
        return 'currency'; // افتراضي
    }

    /**
     * الحصول على نسبة الربح الافتراضية
     */
    getDefaultProfitPercentage(type) {
        const defaults = {
            'currency': 85,
            'cryptocurrency': 80,
            'stock': 75,
            'commodity': 70
        };
        return defaults[type] || 80;
    }

    /**
     * الحصول على أوقات انتهاء الصلاحية الافتراضية
     */
    getDefaultExpirationTimes() {
        return [30, 60, 120, 180, 300, 600, 900, 1800, 3600]; // بالثواني
    }

    /**
     * الحصول على قائمة الأدوات المالية
     */
    getInstruments() {
        return [...this.instruments];
    }

    /**
     * الحصول على أداة مالية محددة
     */
    getInstrument(symbol) {
        return this.instruments.find(inst => inst.symbol === symbol.toUpperCase());
    }

    /**
     * الحصول على الأدوات النشطة فقط
     */
    getActiveInstruments() {
        return this.instruments.filter(inst => inst.isActive);
    }

    /**
     * الحصول على الأدوات حسب النوع
     */
    getInstrumentsByType(type) {
        return this.instruments.filter(inst => inst.type === type);
    }

    /**
     * تحديث الأدوات المالية
     */
    async refresh() {
        await this.fetchInstruments();
    }

    /**
     * حفظ البيانات
     */
    async saveData() {
        try {
            const instrumentsFile = path.join(this.dataPath, 'instruments.json');
            const data = {
                instruments: this.instruments,
                lastUpdate: this.lastUpdate,
                count: this.instruments.length
            };
            
            await fs.writeFile(instrumentsFile, JSON.stringify(data, null, 2));
            
        } catch (error) {
            this.logger.error('فشل في حفظ بيانات الأدوات المالية:', error);
        }
    }

    /**
     * الحصول على حالة مدير الأدوات المالية
     */
    getStatus() {
        const types = {};
        this.instruments.forEach(inst => {
            types[inst.type] = (types[inst.type] || 0) + 1;
        });

        return {
            totalInstruments: this.instruments.length,
            activeInstruments: this.instruments.filter(inst => inst.isActive).length,
            types: types,
            lastUpdate: this.lastUpdate
        };
    }
}

module.exports = { InstrumentsManager };
