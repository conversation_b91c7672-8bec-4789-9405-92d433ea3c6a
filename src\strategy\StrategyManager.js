/**
 * مدير الاستراتيجيات
 */

const { EventEmitter } = require('events');
const fs = require('fs').promises;
const path = require('path');

class StrategyManager extends EventEmitter {
    constructor(config, logger) {
        super();
        this.config = config;
        this.logger = logger;
        this.strategies = new Map();
        this.activeStrategy = null;
        this.isRunning = false;
        this.isPaused = false;
        this.signalHistory = [];
        this.dataPath = config.get('data.dataPath') || './data';
        this.strategyConfig = config.get('strategy') || {};
    }

    /**
     * تهيئة مدير الاستراتيجيات
     */
    async initialize() {
        try {
            this.logger.info('🧠 تهيئة مدير الاستراتيجيات...');
            
            // إنشاء مجلد البيانات
            await this.ensureDataDirectory();
            
            // تحميل الاستراتيجيات المتاحة
            await this.loadStrategies();
            
            // تحميل بيانات الإشارات المحفوظة
            await this.loadSignalHistory();
            
            // تفعيل الاستراتيجية الافتراضية
            if (this.strategyConfig.name && this.strategyConfig.enabled) {
                try {
                    await this.activateStrategy(this.strategyConfig.name);
                } catch (error) {
                    this.logger.warn(`⚠️ فشل في تفعيل الاستراتيجية ${this.strategyConfig.name}:`, error.message);
                    // تفعيل استراتيجية افتراضية
                    if (this.strategies.has('simple_test')) {
                        this.logger.info('🔄 تفعيل الاستراتيجية الافتراضية: simple_test');
                        await this.activateStrategy('simple_test');
                    }
                }
            }
            
            this.logger.info('✅ تم تهيئة مدير الاستراتيجيات بنجاح');
            
        } catch (error) {
            this.logger.error('❌ فشل في تهيئة مدير الاستراتيجيات:', error);
            throw error;
        }
    }

    /**
     * التأكد من وجود مجلد البيانات
     */
    async ensureDataDirectory() {
        try {
            const strategyDir = path.join(this.dataPath, 'strategies');
            await fs.mkdir(strategyDir, { recursive: true });
        } catch (error) {
            // تجاهل الخطأ إذا كان المجلد موجوداً
        }
    }

    /**
     * تحميل الاستراتيجيات المتاحة
     */
    async loadStrategies() {
        try {
            // تحميل الاستراتيجيات المدمجة
            this.loadBuiltInStrategies();
            
            // تحميل الاستراتيجيات المخصصة من الملفات
            await this.loadCustomStrategies();
            
            this.logger.info(`📊 تم تحميل ${this.strategies.size} استراتيجية`);
            
        } catch (error) {
            this.logger.error('فشل في تحميل الاستراتيجيات:', error);
        }
    }

    /**
     * تحميل الاستراتيجيات المدمجة
     */
    loadBuiltInStrategies() {
        // استراتيجية بسيطة للاختبار
        this.strategies.set('simple_test', {
            name: 'Simple Test Strategy',
            description: 'استراتيجية بسيطة للاختبار',
            parameters: {
                timeframe: '5m',
                minAmount: 1,
                maxAmount: 10
            },
            analyze: this.simpleTestStrategy.bind(this)
        });

        // استراتيجية عشوائية للاختبار
        this.strategies.set('random', {
            name: 'Random Strategy',
            description: 'استراتيجية عشوائية للاختبار',
            parameters: {
                probability: 0.3, // 30% احتمال إنشاء إشارة
                timeframe: '5m'
            },
            analyze: this.randomStrategy.bind(this)
        });

        // استراتيجية الاتجاه البسيط
        this.strategies.set('trend_following', {
            name: 'Trend Following',
            description: 'استراتيجية تتبع الاتجاه',
            parameters: {
                periodShort: 5,
                periodLong: 20,
                minTrendStrength: 0.6
            },
            analyze: this.trendFollowingStrategy.bind(this)
        });
    }

    /**
     * تحميل الاستراتيجيات المخصصة
     */
    async loadCustomStrategies() {
        try {
            const strategiesDir = path.join(__dirname, 'strategies');
            
            // التحقق من وجود مجلد الاستراتيجيات
            try {
                await fs.access(strategiesDir);
            } catch {
                // إنشاء المجلد إذا لم يكن موجوداً
                await fs.mkdir(strategiesDir, { recursive: true });
                return;
            }
            
            const files = await fs.readdir(strategiesDir);
            
            for (const file of files) {
                if (file.endsWith('.js')) {
                    try {
                        const strategyPath = path.join(strategiesDir, file);
                        const strategy = require(strategyPath);
                        
                        if (strategy.name && strategy.analyze) {
                            this.strategies.set(strategy.name, strategy);
                            this.logger.info(`📈 تم تحميل استراتيجية: ${strategy.name}`);
                        }
                    } catch (error) {
                        this.logger.warn(`⚠️ فشل في تحميل استراتيجية ${file}:`, error.message);
                    }
                }
            }
            
        } catch (error) {
            this.logger.warn('فشل في تحميل الاستراتيجيات المخصصة:', error);
        }
    }

    /**
     * تحميل تاريخ الإشارات
     */
    async loadSignalHistory() {
        try {
            const signalsFile = path.join(this.dataPath, 'strategies', 'signals.json');
            const data = await fs.readFile(signalsFile, 'utf8');
            this.signalHistory = JSON.parse(data);
            
            this.logger.info(`📥 تم تحميل ${this.signalHistory.length} إشارة من التاريخ`);
            
        } catch (error) {
            this.logger.info('📝 لا توجد إشارات محفوظة، بدء جديد');
            this.signalHistory = [];
        }
    }

    /**
     * تفعيل استراتيجية
     */
    async activateStrategy(strategyName) {
        const strategy = this.strategies.get(strategyName);
        
        if (!strategy) {
            throw new Error(`الاستراتيجية غير موجودة: ${strategyName}`);
        }

        this.activeStrategy = {
            ...strategy,
            name: strategyName,
            parameters: { ...strategy.parameters, ...this.strategyConfig.parameters }
        };

        this.logger.strategy(`تم تفعيل الاستراتيجية: ${strategy.name}`);
    }

    /**
     * معالجة تحديث الشمعة
     */
    async processCandleUpdate(candleData) {
        if (!this.isRunning || this.isPaused || !this.activeStrategy) {
            return;
        }

        try {
            // تحليل البيانات باستخدام الاستراتيجية النشطة
            const signal = await this.activeStrategy.analyze(candleData, this.activeStrategy.parameters);
            
            if (signal) {
                await this.processSignal(signal);
            }
            
        } catch (error) {
            this.logger.error('خطأ في معالجة تحديث الشمعة:', error);
        }
    }

    /**
     * معالجة الإشارة
     */
    async processSignal(signal) {
        try {
            // إضافة معلومات إضافية للإشارة
            const enhancedSignal = {
                ...signal,
                timestamp: new Date().toISOString(),
                strategy: this.activeStrategy.name,
                id: this.generateSignalId()
            };

            // حفظ الإشارة في التاريخ
            this.signalHistory.push(enhancedSignal);
            
            // الاحتفاظ بآخر 1000 إشارة فقط
            if (this.signalHistory.length > 1000) {
                this.signalHistory = this.signalHistory.slice(-1000);
            }

            // إرسال الإشارة
            this.emit('signal', enhancedSignal);
            
            this.logger.strategy(`إشارة جديدة: ${signal.direction.toUpperCase()} ${signal.symbol}`, {
                confidence: signal.confidence,
                price: signal.price
            });

            // حفظ البيانات
            await this.saveSignalHistory();
            
        } catch (error) {
            this.logger.error('خطأ في معالجة الإشارة:', error);
        }
    }

    /**
     * إنشاء معرف إشارة
     */
    generateSignalId() {
        return 'signal_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
    }

    /**
     * بدء تشغيل الاستراتيجية
     */
    start() {
        if (!this.activeStrategy) {
            throw new Error('لا توجد استراتيجية نشطة');
        }

        this.isRunning = true;
        this.isPaused = false;
        this.logger.strategy('تم بدء تشغيل الاستراتيجية');
    }

    /**
     * إيقاف الاستراتيجية
     */
    async stop() {
        this.isRunning = false;
        this.isPaused = false;
        
        // حفظ البيانات النهائية
        await this.saveSignalHistory();
        
        this.logger.strategy('تم إيقاف الاستراتيجية');
    }

    /**
     * إيقاف مؤقت للاستراتيجية
     */
    pause() {
        this.isPaused = true;
        this.logger.strategy('تم إيقاف الاستراتيجية مؤقتاً');
    }

    /**
     * استئناف الاستراتيجية
     */
    resume() {
        this.isPaused = false;
        this.logger.strategy('تم استئناف الاستراتيجية');
    }

    /**
     * استراتيجية الاختبار البسيط
     */
    async simpleTestStrategy(candleData, parameters) {
        // إشارة بسيطة كل 10 شموع
        const shouldSignal = Math.random() < 0.1; // 10% احتمال
        
        if (shouldSignal) {
            return {
                symbol: candleData.symbol,
                direction: Math.random() > 0.5 ? 'call' : 'put',
                confidence: Math.random() * 0.5 + 0.5, // 50-100%
                price: candleData.candle ? candleData.candle.close : Math.random() * 100,
                amount: parameters.minAmount,
                expiration: 60,
                reason: 'Simple test signal'
            };
        }
        
        return null;
    }

    /**
     * استراتيجية عشوائية
     */
    async randomStrategy(candleData, parameters) {
        const shouldSignal = Math.random() < parameters.probability;
        
        if (shouldSignal) {
            return {
                symbol: candleData.symbol,
                direction: Math.random() > 0.5 ? 'call' : 'put',
                confidence: Math.random(),
                price: candleData.candle ? candleData.candle.close : Math.random() * 100,
                amount: 1,
                expiration: 60,
                reason: 'Random signal'
            };
        }
        
        return null;
    }

    /**
     * استراتيجية تتبع الاتجاه
     */
    async trendFollowingStrategy(candleData, parameters) {
        // هذه استراتيجية مبسطة - ستحتاج لتطوير أكثر تعقيداً
        if (!candleData.candle) return null;
        
        const candle = candleData.candle;
        const isGreenCandle = candle.close > candle.open;
        const candleSize = Math.abs(candle.close - candle.open);
        const averageSize = (candle.high - candle.low) * 0.6;
        
        // إشارة إذا كانت الشمعة كبيرة نسبياً
        if (candleSize > averageSize) {
            return {
                symbol: candleData.symbol,
                direction: isGreenCandle ? 'call' : 'put',
                confidence: Math.min(candleSize / averageSize, 1.0),
                price: candle.close,
                amount: parameters.minAmount || 1,
                expiration: 60,
                reason: `Strong ${isGreenCandle ? 'bullish' : 'bearish'} candle`
            };
        }
        
        return null;
    }

    /**
     * حفظ تاريخ الإشارات
     */
    async saveSignalHistory() {
        try {
            const signalsFile = path.join(this.dataPath, 'strategies', 'signals.json');
            await fs.writeFile(signalsFile, JSON.stringify(this.signalHistory, null, 2));
        } catch (error) {
            this.logger.error('فشل في حفظ تاريخ الإشارات:', error);
        }
    }

    /**
     * الحصول على الاستراتيجيات المتاحة
     */
    getAvailableStrategies() {
        return Array.from(this.strategies.entries()).map(([name, strategy]) => ({
            name,
            displayName: strategy.name,
            description: strategy.description,
            parameters: strategy.parameters
        }));
    }

    /**
     * الحصول على الاستراتيجية النشطة
     */
    getActiveStrategy() {
        return this.activeStrategy;
    }

    /**
     * الحصول على تاريخ الإشارات
     */
    getSignalHistory() {
        return [...this.signalHistory];
    }

    /**
     * الحصول على حالة مدير الاستراتيجيات
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            isPaused: this.isPaused,
            activeStrategy: this.activeStrategy ? this.activeStrategy.name : null,
            availableStrategies: this.strategies.size,
            signalCount: this.signalHistory.length,
            lastSignal: this.signalHistory.length > 0 ? 
                this.signalHistory[this.signalHistory.length - 1].timestamp : null
        };
    }

    /**
     * الحصول على إحصائيات الإشارات
     */
    getSignalStatistics() {
        if (this.signalHistory.length === 0) {
            return null;
        }

        const signals = this.signalHistory;
        const callSignals = signals.filter(s => s.direction === 'call').length;
        const putSignals = signals.filter(s => s.direction === 'put').length;
        const avgConfidence = signals.reduce((sum, s) => sum + (s.confidence || 0), 0) / signals.length;

        // إحصائيات حسب الرمز
        const symbolStats = {};
        signals.forEach(signal => {
            if (!symbolStats[signal.symbol]) {
                symbolStats[signal.symbol] = { call: 0, put: 0, total: 0 };
            }
            symbolStats[signal.symbol][signal.direction]++;
            symbolStats[signal.symbol].total++;
        });

        return {
            totalSignals: signals.length,
            callSignals,
            putSignals,
            callPercentage: ((callSignals / signals.length) * 100).toFixed(2),
            putPercentage: ((putSignals / signals.length) * 100).toFixed(2),
            averageConfidence: (avgConfidence * 100).toFixed(2),
            symbolStats,
            signalsToday: this.getSignalsToday(),
            signalsThisWeek: this.getSignalsThisWeek()
        };
    }

    /**
     * الحصول على إشارات اليوم
     */
    getSignalsToday() {
        const today = new Date().toISOString().split('T')[0];
        return this.signalHistory.filter(signal => 
            signal.timestamp.startsWith(today)
        ).length;
    }

    /**
     * الحصول على إشارات هذا الأسبوع
     */
    getSignalsThisWeek() {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        
        return this.signalHistory.filter(signal => 
            new Date(signal.timestamp) >= weekAgo
        ).length;
    }
}

module.exports = { StrategyManager };
