{"summary": {"totalMessages": 17094, "decodedMessages": 0, "connectionMessages": 3, "authMessages": 3, "instrumentsMessages": 18, "priceStreamMessages": 7787, "tradingDataMessages": 0, "instrumentsDataFound": false}, "criticalMessages": {"connection": [{"timestamp": "2025-07-01T23:37:52.106Z", "direction": "received", "sessionId": "wzpsexUJgNs37vTFDbn2", "pingInterval": 25000, "pingTimeout": 5000}, {"timestamp": "2025-07-01T23:49:24.671Z", "direction": "received", "sessionId": "0qKn6ijWbH57vRFPRECD", "pingInterval": 25000, "pingTimeout": 5000}, {"timestamp": "2025-07-01T23:52:55.563Z", "direction": "received", "sessionId": "ie7RuGs7hfanlZZ5RG1F", "pingInterval": 25000, "pingTimeout": 5000}], "authorization": [{"timestamp": "2025-07-01T23:37:52.140Z", "direction": "sent", "sessionId": "77YkYUgHWXVhKsdZbuzX6gjtnpR4uoPq6Kea8RmV", "isDemo": false, "tournamentId": 0}, {"timestamp": "2025-07-01T23:49:24.721Z", "direction": "sent", "sessionId": "77YkYUgHWXVhKsdZbuzX6gjtnpR4uoPq6Kea8RmV", "isDemo": true, "tournamentId": 0}, {"timestamp": "2025-07-01T23:52:55.654Z", "direction": "sent", "sessionId": "77YkYUgHWXVhKsdZbuzX6gjtnpR4uoPq6Kea8RmV", "isDemo": true, "tournamentId": 0}], "instruments": [{"timestamp": "2025-07-01T23:37:52.432Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:38:07.169Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:40:09.341Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:40:13.968Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:40:14.720Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:40:14.860Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:42:07.032Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:49:25.037Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:50:08.235Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:50:12.964Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:50:13.166Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:52:07.131Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:52:55.701Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:54:06.643Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:55:34.613Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:55:34.695Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-01T23:57:18.975Z", "direction": "received", "messageType": "instruments_list_request"}, {"timestamp": "2025-07-02T00:02:55.367Z", "direction": "received", "messageType": "instruments_list_request"}], "priceStreams": [{"timestamp": "2025-07-01T23:37:54.174Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:54.444Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:54.960Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:55.210Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:55.527Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:55.961Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:56.375Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:56.454Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:56.998Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:57.229Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:57.483Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:57.978Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:58.462Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:58.736Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:59.018Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:59.500Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:59.565Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:37:59.980Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:00.334Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:01.130Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:01.579Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:01.980Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:02.483Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:02.731Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:03.499Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:03.555Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:03.991Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:04.332Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:04.472Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:04.999Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:05.138Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:06.751Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:07.023Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:07.557Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:08.423Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:09.068Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:09.297Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:09.710Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:10.015Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:10.494Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:10.833Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:11.479Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:11.608Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:11.994Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:12.347Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:12.998Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:13.136Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:13.502Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:13.984Z", "direction": "received", "messageType": "price_stream_request"}, {"timestamp": "2025-07-01T23:38:14.496Z", "direction": "received", "messageType": "price_stream_request"}], "tradingData": []}}