const fs = require('fs');

// قراءة ملف الرسائل
const messages = JSON.parse(fs.readFileSync('ws_messages.json', 'utf8'));

// دالة لفك تشفير Base64
function decodeBase64(str) {
    try {
        return Buffer.from(str, 'base64').toString('utf8');
    } catch (e) {
        return null;
    }
}

console.log('بدء فك تشفير وتحليل رسائل WebSocket...');

// البحث عن الرسائل المشفرة بـ Base64
const importantMessages = {
    instrumentsData: [],
    priceStreams: [],
    tradingData: [],
    connectionInfo: [],
    authMessages: []
};

let totalDecoded = 0;
let instrumentsFound = false;

for (let i = 0; i < messages.length; i++) {
    const msg = messages[i];
    
    // تحليل رسائل Socket.IO
    const socketIOMatch = msg.message.match(/^(\d+)(.*)$/);
    if (!socketIOMatch) continue;
    
    const type = parseInt(socketIOMatch[1]);
    const payload = socketIOMatch[2];
    
    // رسائل الاتصال (نوع 0)
    if (type === 0) {
        try {
            const data = JSON.parse(payload);
            importantMessages.connectionInfo.push({
                timestamp: msg.timestamp,
                direction: msg.type,
                sessionId: data.sid,
                pingInterval: data.pingInterval,
                pingTimeout: data.pingTimeout
            });
        } catch (e) {}
    }
    
    // رسائل التفويض (نوع 42)
    else if (type === 42) {
        try {
            const eventData = JSON.parse(payload);
            if (eventData[0] === 'authorization') {
                importantMessages.authMessages.push({
                    timestamp: msg.timestamp,
                    direction: msg.type,
                    sessionId: eventData[1].session,
                    isDemo: eventData[1].isDemo === 1,
                    tournamentId: eventData[1].tournamentId
                });
            }
        } catch (e) {}
    }
    
    // رسائل البيانات المشفرة (نوع 451)
    else if (type === 451) {
        // فحص إذا كانت رسالة قائمة الأدوات
        if (payload.includes('instruments/list')) {
            importantMessages.instrumentsData.push({
                timestamp: msg.timestamp,
                direction: msg.type,
                messageType: 'instruments_list_request'
            });
        }
        // فحص إذا كانت رسالة تحديث الأسعار
        else if (payload.includes('quotes/stream')) {
            importantMessages.priceStreams.push({
                timestamp: msg.timestamp,
                direction: msg.type,
                messageType: 'price_stream_request'
            });
        }
    }
    
    // البيانات المشفرة الأخرى
    else {
        // محاولة فك تشفير Base64
        const decoded = decodeBase64(payload);
        if (decoded) {
            totalDecoded++;
            
            // فحص إذا كانت البيانات تحتوي على معلومات الأدوات المالية
            if (decoded.includes('EURUSD') || decoded.includes('BTCUSD') || decoded.includes('currency') || decoded.includes('cryptocurrency')) {
                try {
                    // محاولة تحليل البيانات كـ JSON
                    const data = JSON.parse(decoded);
                    if (Array.isArray(data) && data.length > 0) {
                        // هذه قائمة الأدوات المالية
                        const instruments = data.filter(item => Array.isArray(item) && item.length > 10);
                        if (instruments.length > 0) {
                            instrumentsFound = true;
                            importantMessages.instrumentsData.push({
                                timestamp: msg.timestamp,
                                direction: msg.type,
                                messageType: 'instruments_data',
                                instrumentsCount: instruments.length,
                                sampleInstruments: instruments.slice(0, 10).map(inst => ({
                                    id: inst[0],
                                    symbol: inst[1],
                                    name: inst[2],
                                    type: inst[3],
                                    precision: inst[4],
                                    percentage: inst[5],
                                    isOTC: inst[9],
                                    isActive: inst[14],
                                    currentPrice: inst[16],
                                    priceChange: inst[17]
                                }))
                            });
                        }
                    }
                } catch (e) {
                    // إذا لم تكن JSON، قد تكون بيانات أسعار
                    if (decoded.length > 100) {
                        importantMessages.tradingData.push({
                            timestamp: msg.timestamp,
                            direction: msg.type,
                            messageType: 'encoded_data',
                            dataLength: decoded.length,
                            preview: decoded.substring(0, 200)
                        });
                    }
                }
            }
            
            // فحص بيانات الأسعار المباشرة
            else if (decoded.includes('[') && decoded.includes(']')) {
                try {
                    const priceData = JSON.parse(decoded);
                    if (Array.isArray(priceData)) {
                        importantMessages.priceStreams.push({
                            timestamp: msg.timestamp,
                            direction: msg.type,
                            messageType: 'price_data',
                            dataCount: priceData.length,
                            sampleData: priceData.slice(0, 5)
                        });
                    }
                } catch (e) {}
            }
        }
    }
}

console.log('\n=== نتائج فك التشفير والتحليل ===');
console.log(`إجمالي الرسائل: ${messages.length}`);
console.log(`الرسائل المفكوكة التشفير: ${totalDecoded}`);
console.log(`رسائل الاتصال: ${importantMessages.connectionInfo.length}`);
console.log(`رسائل التفويض: ${importantMessages.authMessages.length}`);
console.log(`رسائل الأدوات المالية: ${importantMessages.instrumentsData.length}`);
console.log(`رسائل تحديث الأسعار: ${importantMessages.priceStreams.length}`);
console.log(`بيانات التداول الأخرى: ${importantMessages.tradingData.length}`);
console.log(`تم العثور على بيانات الأدوات: ${instrumentsFound ? 'نعم' : 'لا'}`);

// حفظ النتائج النهائية
fs.writeFileSync('trading_bot_messages.json', JSON.stringify({
    summary: {
        totalMessages: messages.length,
        decodedMessages: totalDecoded,
        connectionMessages: importantMessages.connectionInfo.length,
        authMessages: importantMessages.authMessages.length,
        instrumentsMessages: importantMessages.instrumentsData.length,
        priceStreamMessages: importantMessages.priceStreams.length,
        tradingDataMessages: importantMessages.tradingData.length,
        instrumentsDataFound: instrumentsFound
    },
    criticalMessages: {
        connection: importantMessages.connectionInfo,
        authorization: importantMessages.authMessages,
        instruments: importantMessages.instrumentsData,
        priceStreams: importantMessages.priceStreams.slice(0, 50),
        tradingData: importantMessages.tradingData.slice(0, 20)
    }
}, null, 2));

console.log('\nتم حفظ الرسائل المهمة لروبوت التداول في ملف trading_bot_messages.json');

// إنشاء ملخص للرسائل المهمة لروبوت التداول
const botGuide = {
    "دليل إنشاء روبوت التداول": {
        "1. رسائل الاتصال الأساسية": {
            "وصف": "رسائل تأسيس الاتصال مع الخادم",
            "أهمية": "عالية جداً - مطلوبة لبدء أي جلسة تداول",
            "مثال": importantMessages.connectionInfo[0] || "غير متوفر"
        },
        "2. رسائل التفويض": {
            "وصف": "رسائل تسجيل الدخول والتفويض",
            "أهمية": "عالية جداً - مطلوبة للوصول للحساب",
            "مثال": importantMessages.authMessages[0] || "غير متوفر"
        },
        "3. رسائل قوائم الأدوات المالية": {
            "وصف": "قوائم العملات والأسهم والعملات المشفرة المتاحة",
            "أهمية": "عالية - مطلوبة لمعرفة الأدوات المتاحة للتداول",
            "عدد_الرسائل": importantMessages.instrumentsData.length
        },
        "4. رسائل تحديث الأسعار المباشرة": {
            "وصف": "تحديثات الأسعار في الوقت الفعلي",
            "أهمية": "عالية جداً - أساس اتخاذ قرارات التداول",
            "عدد_الرسائل": importantMessages.priceStreams.length
        },
        "5. بيانات التداول الإضافية": {
            "وصف": "بيانات مشفرة أخرى قد تحتوي على معلومات مهمة",
            "أهمية": "متوسطة - قد تحتوي على إشارات تداول",
            "عدد_الرسائل": importantMessages.tradingData.length
        }
    }
};

fs.writeFileSync('bot_development_guide.json', JSON.stringify(botGuide, null, 2));
console.log('تم إنشاء دليل تطوير روبوت التداول في ملف bot_development_guide.json');
