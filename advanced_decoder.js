const fs = require('fs');

// قراءة ملف الرسائل
const messages = JSON.parse(fs.readFileSync('ws_messages.json', 'utf8'));

// دالة لفك تشفير Base64
function decodeBase64(str) {
    try {
        return Buffer.from(str, 'base64').toString('utf8');
    } catch (e) {
        return null;
    }
}

// دالة لتحليل البيانات المشفرة
function parseInstrumentData(decodedData) {
    try {
        // البيانات تأتي كـ JSON array
        const data = JSON.parse(decodedData);
        const instruments = [];
        
        for (const item of data) {
            if (Array.isArray(item) && item.length > 10) {
                const instrument = {
                    id: item[0],
                    symbol: item[1],
                    name: item[2],
                    type: item[3], // currency, cryptocurrency, stock
                    precision: item[4],
                    percentage: item[5],
                    timeframe: item[6],
                    minTime: item[7],
                    maxTime: item[8],
                    isOTC: item[9],
                    groupId: item[10],
                    parentId: item[11],
                    options: item[12], // call/put options with times
                    timestamp: item[13],
                    isActive: item[14],
                    priceData: item[15], // price data for different timeframes
                    currentPrice: item[16],
                    priceChange: item[17],
                    high: item[18],
                    low: item[19],
                    volume: item[20],
                    bid: item[21],
                    ask: item[22],
                    spread: item[23],
                    performance: {
                        daily: item[24],
                        weekly: item[25],
                        monthly: item[26]
                    }
                };
                instruments.push(instrument);
            }
        }
        
        return instruments;
    } catch (e) {
        return null;
    }
}

// دالة لتحليل رسائل الأسعار المباشرة
function parsePriceUpdate(message) {
    try {
        // رسائل الأسعار تأتي بصيغة مختلفة
        if (message.includes('quotes/stream')) {
            return {
                type: 'price_stream',
                data: message
            };
        }
        
        // فك تشفير البيانات المشفرة
        const decoded = decodeBase64(message);
        if (decoded && decoded.startsWith('[[')) {
            const priceData = JSON.parse(decoded);
            return {
                type: 'price_update',
                instruments: priceData.map(item => ({
                    symbol: item[0],
                    price: item[1],
                    change: item[2],
                    timestamp: item[3]
                }))
            };
        }
        
        return null;
    } catch (e) {
        return null;
    }
}

console.log('بدء التحليل المتقدم لرسائل WebSocket...');

// تحليل جميع الرسائل
const analysisResults = {
    connectionMessages: [],
    authorizationMessages: [],
    instrumentsList: [],
    priceUpdates: [],
    tradingSignals: [],
    unknownMessages: []
};

let instrumentsData = [];

for (let i = 0; i < messages.length; i++) {
    const msg = messages[i];
    const socketIOParsed = msg.message.match(/^(\d+)(.*)$/);
    
    if (!socketIOParsed) continue;
    
    const type = parseInt(socketIOParsed[1]);
    const payload = socketIOParsed[2];
    
    // رسائل الاتصال
    if (type === 0) {
        analysisResults.connectionMessages.push({
            timestamp: msg.timestamp,
            direction: msg.type,
            data: JSON.parse(payload)
        });
    }
    
    // رسائل التفويض والأحداث
    else if (type === 42) {
        try {
            const eventData = JSON.parse(payload);
            if (eventData[0] === 'authorization') {
                analysisResults.authorizationMessages.push({
                    timestamp: msg.timestamp,
                    direction: msg.type,
                    sessionId: eventData[1].session,
                    isDemo: eventData[1].isDemo,
                    tournamentId: eventData[1].tournamentId
                });
            }
        } catch (e) {}
    }
    
    // رسائل البيانات المشفرة
    else if (type === 451) {
        if (payload.includes('instruments/list')) {
            // هذه رسالة قائمة الأدوات المالية
            analysisResults.instrumentsList.push({
                timestamp: msg.timestamp,
                direction: msg.type,
                messageType: 'instruments_list'
            });
        } else if (payload.includes('quotes/stream')) {
            // هذه رسالة تحديث الأسعار
            analysisResults.priceUpdates.push({
                timestamp: msg.timestamp,
                direction: msg.type,
                messageType: 'price_stream'
            });
        }
    }
    
    // البيانات المشفرة بـ Base64
    else {
        const decoded = decodeBase64(payload);
        if (decoded) {
            // محاولة تحليل بيانات الأدوات المالية
            const instruments = parseInstrumentData(decoded);
            if (instruments && instruments.length > 0) {
                instrumentsData = instruments;
                analysisResults.instrumentsList.push({
                    timestamp: msg.timestamp,
                    direction: msg.type,
                    instrumentsCount: instruments.length,
                    instruments: instruments.slice(0, 5) // أول 5 أدوات كعينة
                });
            }
            
            // محاولة تحليل تحديثات الأسعار
            const priceUpdate = parsePriceUpdate(payload);
            if (priceUpdate) {
                analysisResults.priceUpdates.push({
                    timestamp: msg.timestamp,
                    direction: msg.type,
                    ...priceUpdate
                });
            }
        }
    }
}

console.log('\n=== نتائج التحليل المتقدم ===');
console.log(`رسائل الاتصال: ${analysisResults.connectionMessages.length}`);
console.log(`رسائل التفويض: ${analysisResults.authorizationMessages.length}`);
console.log(`رسائل قوائم الأدوات: ${analysisResults.instrumentsList.length}`);
console.log(`رسائل تحديث الأسعار: ${analysisResults.priceUpdates.length}`);
console.log(`إجمالي الأدوات المالية المكتشفة: ${instrumentsData.length}`);

// حفظ النتائج المفصلة
fs.writeFileSync('detailed_analysis.json', JSON.stringify({
    summary: {
        totalMessages: messages.length,
        connectionMessages: analysisResults.connectionMessages.length,
        authorizationMessages: analysisResults.authorizationMessages.length,
        instrumentsMessages: analysisResults.instrumentsList.length,
        priceUpdateMessages: analysisResults.priceUpdates.length,
        totalInstruments: instrumentsData.length
    },
    connectionFlow: analysisResults.connectionMessages,
    authorization: analysisResults.authorizationMessages,
    instrumentsData: instrumentsData,
    priceUpdates: analysisResults.priceUpdates.slice(0, 20),
    sampleInstruments: instrumentsData.slice(0, 20)
}, null, 2));

console.log('\nتم حفظ التحليل المفصل في ملف detailed_analysis.json');
