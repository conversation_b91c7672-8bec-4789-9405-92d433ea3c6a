const fs = require('fs');

// قراءة ملف الرسائل
const messages = JSON.parse(fs.readFileSync('ws_messages.json', 'utf8'));

// دالة لفك تشفير Base64
function decodeBase64(str) {
    try {
        return Buffer.from(str, 'base64').toString('utf8');
    } catch (e) {
        return null;
    }
}

// دالة لتحليل رسائل Socket.IO
function parseSocketIOMessage(message) {
    // Socket.IO message format: [type][id][namespace][data]
    const match = message.match(/^(\d+)(.*)$/);
    if (!match) return null;
    
    const type = parseInt(match[1]);
    const payload = match[2];
    
    const types = {
        0: 'CONNECT',
        1: 'DISCONNECT', 
        2: 'EVENT',
        3: 'ACK',
        4: 'CONNECT_ERROR',
        40: 'CONNECT_NAMESPACE',
        41: 'DISCONNECT_NAMESPACE',
        42: 'EVENT_NAMESPACE',
        43: 'ACK_NAMESPACE',
        44: 'CONNECT_ERROR_NAMESPACE',
        451: 'BINARY_EVENT'
    };
    
    return {
        type: types[type] || `UNKNOWN_${type}`,
        payload: payload
    };
}

// دالة لاستخراج البيانات المهمة
function extractImportantData(message) {
    const result = {
        original: message.message,
        decoded: null,
        type: null,
        data: null,
        importance: 'low'
    };
    
    // تحليل رسائل Socket.IO
    const socketIOParsed = parseSocketIOMessage(message.message);
    if (socketIOParsed) {
        result.type = socketIOParsed.type;
        result.decoded = socketIOParsed.payload;
        
        // تحليل البيانات JSON في الرسالة
        try {
            if (socketIOParsed.payload.startsWith('[')) {
                const jsonData = JSON.parse(socketIOParsed.payload);
                result.data = jsonData;
                
                // تحديد أهمية الرسالة
                if (jsonData[0]) {
                    const eventName = jsonData[0];
                    if (eventName.includes('quotes') || eventName.includes('price')) {
                        result.importance = 'high';
                    } else if (eventName.includes('instruments') || eventName.includes('authorization')) {
                        result.importance = 'medium';
                    }
                }
            } else if (socketIOParsed.payload.startsWith('{')) {
                result.data = JSON.parse(socketIOParsed.payload);
                result.importance = 'medium';
            }
        } catch (e) {
            // محاولة فك تشفير Base64
            const decoded = decodeBase64(socketIOParsed.payload);
            if (decoded) {
                result.decoded = decoded;
                result.importance = 'medium';
            }
        }
    } else {
        // محاولة فك تشفير Base64 مباشرة
        const decoded = decodeBase64(message.message);
        if (decoded) {
            result.decoded = decoded;
            result.type = 'BASE64_DECODED';
            result.importance = 'medium';
        }
    }
    
    return result;
}

console.log('بدء تحليل رسائل WebSocket...');
console.log(`إجمالي الرسائل: ${messages.length}`);

// تحليل عينة من الرسائل
const sampleSize = Math.min(1000, messages.length);
const analyzedMessages = [];

for (let i = 0; i < sampleSize; i++) {
    const analyzed = extractImportantData(messages[i]);
    analyzed.timestamp = messages[i].timestamp;
    analyzed.direction = messages[i].type;
    analyzedMessages.push(analyzed);
}

// تصنيف الرسائل حسب الأهمية
const highImportance = analyzedMessages.filter(m => m.importance === 'high');
const mediumImportance = analyzedMessages.filter(m => m.importance === 'medium');

console.log('\n=== تحليل الرسائل ===');
console.log(`رسائل عالية الأهمية: ${highImportance.length}`);
console.log(`رسائل متوسطة الأهمية: ${mediumImportance.length}`);

// حفظ النتائج
fs.writeFileSync('analyzed_messages.json', JSON.stringify({
    summary: {
        total: messages.length,
        analyzed: sampleSize,
        high_importance: highImportance.length,
        medium_importance: mediumImportance.length
    },
    high_importance_messages: highImportance.slice(0, 50),
    medium_importance_messages: mediumImportance.slice(0, 50),
    all_analyzed: analyzedMessages
}, null, 2));

console.log('\nتم حفظ النتائج في ملف analyzed_messages.json');
