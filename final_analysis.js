const fs = require('fs');

// قراءة ملف الرسائل
const messages = JSON.parse(fs.readFileSync('ws_messages.json', 'utf8'));

console.log('تحليل نهائي لرسائل WebSocket لإنشاء روبوت التداول...');

// البحث عن الرسالة التي تحتوي على بيانات الأدوات المالية
let instrumentsMessage = null;
let instrumentsData = [];

// البحث عن الرسالة في السطر 24 التي تحتوي على البيانات المفكوكة
for (let i = 20; i < 30; i++) {
    if (messages[i] && messages[i].message && messages[i].message.includes('ADAUSD_otc')) {
        instrumentsMessage = messages[i];
        console.log(`تم العثور على رسالة الأدوات المالية في الرسالة رقم ${i + 1}`);
        break;
    }
}

if (instrumentsMessage) {
    // استخراج البيانات من الرسالة
    const messageContent = instrumentsMessage.message;
    
    // تحليل البيانات - الرسالة تحتوي على مصفوفة من الأدوات المالية
    // كل أداة مالية هي مصفوفة تحتوي على معلومات مفصلة
    
    // استخراج أسماء الأدوات المالية من النص
    const currencyMatches = messageContent.match(/\["?\w+","?[^"]+","?[^"]+","?currency/g) || [];
    const cryptoMatches = messageContent.match(/\["?\w+","?[^"]+","?[^"]+","?cryptocurrency/g) || [];
    const stockMatches = messageContent.match(/\["?\w+","?[^"]+","?[^"]+","?stock/g) || [];
    
    // استخراج معلومات مفصلة عن الأدوات
    const extractInstrumentInfo = (text) => {
        const instruments = [];
        
        // البحث عن أنماط الأدوات المالية
        const patterns = [
            /\[(\d+),"([^"]+)","([^"]+)","(currency|cryptocurrency|stock)",(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),(\d+),\[([^\]]*)\],(\d+),(true|false)/g
        ];
        
        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(text)) !== null) {
                instruments.push({
                    id: parseInt(match[1]),
                    symbol: match[2],
                    name: match[3],
                    type: match[4],
                    precision: parseInt(match[5]),
                    percentage: parseInt(match[6]),
                    timeframe: parseInt(match[7]),
                    minTime: parseInt(match[8]),
                    maxTime: parseInt(match[9]),
                    isOTC: parseInt(match[10]) === 1,
                    groupId: parseInt(match[11]),
                    parentId: parseInt(match[12]),
                    tradingOptions: match[13],
                    timestamp: parseInt(match[14]),
                    isActive: match[15] === 'true'
                });
            }
        });
        
        return instruments;
    };
    
    instrumentsData = extractInstrumentInfo(messageContent);
    
    console.log(`تم استخراج ${instrumentsData.length} أداة مالية`);
}

// تحليل أنواع الرسائل المختلفة
const messageTypes = {
    connection: [],
    authorization: [],
    instrumentsList: [],
    priceUpdates: [],
    binaryData: [],
    heartbeat: []
};

for (let i = 0; i < Math.min(1000, messages.length); i++) {
    const msg = messages[i];
    const message = msg.message;
    
    // تصنيف الرسائل
    if (message.startsWith('0{')) {
        messageTypes.connection.push({
            index: i,
            timestamp: msg.timestamp,
            direction: msg.type,
            data: message
        });
    } else if (message.includes('authorization')) {
        messageTypes.authorization.push({
            index: i,
            timestamp: msg.timestamp,
            direction: msg.type,
            data: message
        });
    } else if (message.includes('instruments/list')) {
        messageTypes.instrumentsList.push({
            index: i,
            timestamp: msg.timestamp,
            direction: msg.type,
            data: message
        });
    } else if (message.includes('quotes/stream')) {
        messageTypes.priceUpdates.push({
            index: i,
            timestamp: msg.timestamp,
            direction: msg.type,
            data: message
        });
    } else if (message.startsWith('451-')) {
        messageTypes.binaryData.push({
            index: i,
            timestamp: msg.timestamp,
            direction: msg.type,
            data: message
        });
    } else if (message === '2' || message === '3') {
        messageTypes.heartbeat.push({
            index: i,
            timestamp: msg.timestamp,
            direction: msg.type,
            data: message
        });
    }
}

// إنشاء دليل شامل لروبوت التداول
const tradingBotGuide = {
    "دليل_إنشاء_روبوت_التداول_لمنصة_Quotex": {
        "1_رسائل_الاتصال_الأساسية": {
            "وصف": "رسائل Socket.IO لتأسيس الاتصال مع الخادم",
            "أهمية": "عالية جداً - مطلوبة لبدء أي جلسة",
            "نوع_الرسالة": "Socket.IO Connection",
            "مثال": messageTypes.connection[0]?.data || "غير متوفر",
            "عدد_الرسائل": messageTypes.connection.length,
            "ملاحظات": "تحتوي على session ID و ping intervals"
        },
        "2_رسائل_التفويض": {
            "وصف": "رسائل تسجيل الدخول والتفويض للوصول للحساب",
            "أهمية": "عالية جداً - مطلوبة للوصول للحساب",
            "نوع_الرسالة": "Authorization",
            "مثال": messageTypes.authorization[0]?.data || "غير متوفر",
            "عدد_الرسائل": messageTypes.authorization.length,
            "ملاحظات": "تحتوي على session token و demo mode flag"
        },
        "3_رسائل_قوائم_الأدوات_المالية": {
            "وصف": "طلب قوائم العملات والأسهم والعملات المشفرة",
            "أهمية": "عالية - مطلوبة لمعرفة الأدوات المتاحة",
            "نوع_الرسالة": "Instruments List Request",
            "مثال": messageTypes.instrumentsList[0]?.data || "غير متوفر",
            "عدد_الرسائل": messageTypes.instrumentsList.length,
            "الأدوات_المكتشفة": instrumentsData.length
        },
        "4_رسائل_تحديث_الأسعار": {
            "وصف": "طلب تحديثات الأسعار في الوقت الفعلي",
            "أهمية": "عالية جداً - أساس اتخاذ قرارات التداول",
            "نوع_الرسالة": "Price Stream Request",
            "مثال": messageTypes.priceUpdates[0]?.data || "غير متوفر",
            "عدد_الرسائل": messageTypes.priceUpdates.length
        },
        "5_رسائل_البيانات_المشفرة": {
            "وصف": "بيانات مشفرة تحتوي على معلومات الأدوات والأسعار",
            "أهمية": "عالية - تحتوي على البيانات الفعلية",
            "نوع_الرسالة": "Binary Data",
            "عدد_الرسائل": messageTypes.binaryData.length,
            "ملاحظات": "تحتاج لفك تشفير Base64"
        },
        "6_رسائل_Heartbeat": {
            "وصف": "رسائل للحفاظ على الاتصال نشطاً",
            "أهمية": "متوسطة - مطلوبة لاستمرار الاتصال",
            "نوع_الرسالة": "Heartbeat/Ping",
            "عدد_الرسائل": messageTypes.heartbeat.length
        }
    },
    "الأدوات_المالية_المتاحة": {
        "إجمالي_الأدوات": instrumentsData.length,
        "العملات_التقليدية": instrumentsData.filter(i => i.type === 'currency').length,
        "العملات_المشفرة": instrumentsData.filter(i => i.type === 'cryptocurrency').length,
        "الأسهم": instrumentsData.filter(i => i.type === 'stock').length,
        "عينة_من_الأدوات": instrumentsData.slice(0, 10)
    },
    "خطوات_إنشاء_الروبوت": {
        "1": "إنشاء اتصال Socket.IO مع الخادم",
        "2": "إرسال رسالة التفويض مع session token",
        "3": "طلب قائمة الأدوات المالية المتاحة",
        "4": "الاشتراك في تحديثات الأسعار للأدوات المطلوبة",
        "5": "تحليل البيانات المشفرة الواردة",
        "6": "تنفيذ استراتيجية التداول",
        "7": "إرسال أوامر التداول (Call/Put)",
        "8": "مراقبة نتائج الصفقات"
    },
    "ملاحظات_مهمة": {
        "تشفير_البيانات": "البيانات مشفرة بـ Base64 وتحتاج لفك تشفير",
        "تحديث_الأسعار": "الأسعار تتحدث في الوقت الفعلي عبر WebSocket",
        "أنواع_الصفقات": "Call (صعود) و Put (هبوط)",
        "أوقات_التداول": "مختلفة حسب نوع الأداة المالية",
        "حساب_التجريبي": "يمكن استخدام الحساب التجريبي للاختبار"
    }
};

// حفظ النتائج النهائية
fs.writeFileSync('trading_bot_final_guide.json', JSON.stringify({
    summary: {
        totalMessages: messages.length,
        analyzedMessages: Math.min(1000, messages.length),
        connectionMessages: messageTypes.connection.length,
        authorizationMessages: messageTypes.authorization.length,
        instrumentsMessages: messageTypes.instrumentsList.length,
        priceUpdateMessages: messageTypes.priceUpdates.length,
        binaryDataMessages: messageTypes.binaryData.length,
        heartbeatMessages: messageTypes.heartbeat.length,
        discoveredInstruments: instrumentsData.length
    },
    tradingBotGuide: tradingBotGuide,
    messageTypes: messageTypes,
    instrumentsData: instrumentsData,
    sampleMessages: {
        connection: messageTypes.connection.slice(0, 3),
        authorization: messageTypes.authorization.slice(0, 3),
        instrumentsList: messageTypes.instrumentsList.slice(0, 3),
        priceUpdates: messageTypes.priceUpdates.slice(0, 5),
        binaryData: messageTypes.binaryData.slice(0, 3)
    }
}, null, 2));

console.log('\n=== ملخص التحليل النهائي ===');
console.log(`إجمالي الرسائل المحللة: ${Math.min(1000, messages.length)} من ${messages.length}`);
console.log(`رسائل الاتصال: ${messageTypes.connection.length}`);
console.log(`رسائل التفويض: ${messageTypes.authorization.length}`);
console.log(`رسائل قوائم الأدوات: ${messageTypes.instrumentsList.length}`);
console.log(`رسائل تحديث الأسعار: ${messageTypes.priceUpdates.length}`);
console.log(`رسائل البيانات المشفرة: ${messageTypes.binaryData.length}`);
console.log(`رسائل Heartbeat: ${messageTypes.heartbeat.length}`);
console.log(`الأدوات المالية المكتشفة: ${instrumentsData.length}`);

console.log('\nتم حفظ الدليل النهائي في ملف trading_bot_final_guide.json');
console.log('\n=== أهم الرسائل لإنشاء روبوت التداول ===');
console.log('1. رسائل الاتصال (Socket.IO Connection) - عالية الأهمية');
console.log('2. رسائل التفويض (Authorization) - عالية الأهمية');
console.log('3. رسائل قوائم الأدوات (Instruments List) - عالية الأهمية');
console.log('4. رسائل تحديث الأسعار (Price Updates) - عالية جداً');
console.log('5. رسائل البيانات المشفرة (Binary Data) - عالية الأهمية');
console.log('6. رسائل Heartbeat (Ping/Pong) - متوسطة الأهمية');
