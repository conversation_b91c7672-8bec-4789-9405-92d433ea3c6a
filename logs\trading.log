{"level":"info","message":"Test message","timestamp":"2025-07-11 18:52:09"}
{"level":"info","message":"Test message","timestamp":"2025-07-11 19:44:58"}
{"level":"info","message":"اختبار رسالة السجل","timestamp":"2025-07-11 19:45:05"}
{"level":"info","message":"🚀 بدء تشغيل روبوت التداول...","timestamp":"2025-07-11 19:45:05"}
{"level":"info","message":"✅ تم التحقق من الإعدادات بنجاح","timestamp":"2025-07-11 19:45:05"}
{"level":"info","message":"🔗 بدء الاتصال بمنصة Quotex...","timestamp":"2025-07-11 19:45:05","type":"connection"}
{"level":"info","message":"🔗 تم إطلاق المتصفح بنجاح","timestamp":"2025-07-11 19:45:06","type":"connection"}
{"level":"info","message":"🔗 تم إنشاء صفحة جديدة","timestamp":"2025-07-11 19:45:07","type":"connection"}
{"level":"info","message":"🔗 تم إعداد مراقبة الشبكة","timestamp":"2025-07-11 19:45:07","type":"connection"}
{"level":"info","message":"🔗 الانتقال إلى: https://qxbroker.com/en/demo-trade","timestamp":"2025-07-11 19:45:07","type":"connection"}
{"level":"info","message":"🔗 تم الانتقال إلى المنصة","timestamp":"2025-07-11 19:45:13","type":"connection"}
{"level":"warn","message":"تحذير: قد لا تكون المنصة محملة بالكامل:","timestamp":"2025-07-11 19:45:43"}
{"level":"info","message":"🔗 تم استخراج بيانات الجلسة","sessionId":"غير موجود","timestamp":"2025-07-11 19:45:43","type":"connection"}
{"level":"info","message":"🔗 تم الاتصال بمنصة Quotex بنجاح","timestamp":"2025-07-11 19:45:43","type":"connection"}
{"level":"info","message":"🔗 تم الاتصال بمنصة Quotex بنجاح","timestamp":"2025-07-11 19:45:43","type":"connection"}
{"level":"info","message":"🔧 تهيئة مدير البيانات...","timestamp":"2025-07-11 19:45:43"}
{"level":"info","message":"📝 لا توجد بيانات رصيد محفوظة، بدء جديد","timestamp":"2025-07-11 19:45:43"}
{"level":"info","message":"✅ تم تهيئة مدير الرصيد بنجاح","timestamp":"2025-07-11 19:45:43"}
{"level":"info","message":"📝 لا توجد أدوات مالية محفوظة، سيتم جلبها من المنصة","timestamp":"2025-07-11 19:45:43"}
{"level":"info","message":"📊 جلب الأدوات المالية من المنصة...","timestamp":"2025-07-11 19:45:43"}
{"level":"info","message":"✅ تم جلب 11 أداة مالية","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"✅ تم تهيئة مدير الأدوات المالية بنجاح","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"📥 تم تحميل البيانات التاريخية لـ 0 رمز","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات التاريخية بنجاح","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات الحية بنجاح","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات بنجاح","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"📝 لا توجد بيانات صفقات محفوظة، بدء جديد","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"✅ تم تهيئة مدير التداول بنجاح","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"🧠 تهيئة مدير الاستراتيجيات...","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"📊 تم تحميل 3 استراتيجية","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"📝 لا توجد إشارات محفوظة، بدء جديد","timestamp":"2025-07-11 19:45:49"}
{"level":"error","message":"❌ فشل في تهيئة مدير الاستراتيجيات: الاستراتيجية غير موجودة: custom","stack":"Error: الاستراتيجية غير موجودة: custom\n    at StrategyManager.activateStrategy (C:\\Users\\<USER>\\Desktop\\new\\src\\strategy\\StrategyManager.js:185:19)\n    at StrategyManager.initialize (C:\\Users\\<USER>\\Desktop\\new\\src\\strategy\\StrategyManager.js:41:28)\n    at async TradingBot.start (C:\\Users\\<USER>\\Desktop\\new\\src\\core\\TradingBot.js:94:13)\n    at async testBot (C:\\Users\\<USER>\\Desktop\\new\\test_bot.js:43:13)","timestamp":"2025-07-11 19:45:49"}
{"level":"error","message":"❌ فشل في تشغيل الروبوت: الاستراتيجية غير موجودة: custom","stack":"Error: الاستراتيجية غير موجودة: custom\n    at StrategyManager.activateStrategy (C:\\Users\\<USER>\\Desktop\\new\\src\\strategy\\StrategyManager.js:185:19)\n    at StrategyManager.initialize (C:\\Users\\<USER>\\Desktop\\new\\src\\strategy\\StrategyManager.js:41:28)\n    at async TradingBot.start (C:\\Users\\<USER>\\Desktop\\new\\src\\core\\TradingBot.js:94:13)\n    at async testBot (C:\\Users\\<USER>\\Desktop\\new\\test_bot.js:43:13)","timestamp":"2025-07-11 19:45:49"}
{"level":"info","message":"🚀 بدء تشغيل روبوت التداول Quotex","timestamp":"2025-07-11 19:47:49"}
{"level":"info","message":"🚀 بدء تشغيل روبوت التداول...","timestamp":"2025-07-11 19:47:49"}
{"level":"info","message":"✅ تم التحقق من الإعدادات بنجاح","timestamp":"2025-07-11 19:47:49"}
{"level":"info","message":"🔗 بدء الاتصال بمنصة Quotex...","timestamp":"2025-07-11 19:47:49","type":"connection"}
{"level":"info","message":"🔗 تم إطلاق المتصفح بنجاح","timestamp":"2025-07-11 19:47:50","type":"connection"}
{"level":"info","message":"🔗 تم إنشاء صفحة جديدة","timestamp":"2025-07-11 19:47:50","type":"connection"}
{"level":"info","message":"🔗 تم إعداد مراقبة الشبكة","timestamp":"2025-07-11 19:47:50","type":"connection"}
{"level":"info","message":"🔗 الانتقال إلى: https://qxbroker.com/en/demo-trade","timestamp":"2025-07-11 19:47:50","type":"connection"}
{"level":"info","message":"🔗 تم الانتقال إلى المنصة","timestamp":"2025-07-11 19:47:56","type":"connection"}
{"level":"info","message":"🔗 تم تحميل المنصة بنجاح","timestamp":"2025-07-11 19:48:08","type":"connection"}
{"level":"info","message":"🔗 تم استخراج بيانات الجلسة","sessionId":"غير موجود","timestamp":"2025-07-11 19:48:08","type":"connection"}
{"level":"info","message":"🔗 تم الاتصال بمنصة Quotex بنجاح","timestamp":"2025-07-11 19:48:08","type":"connection"}
{"level":"info","message":"🔗 تم الاتصال بمنصة Quotex بنجاح","timestamp":"2025-07-11 19:48:08","type":"connection"}
{"level":"info","message":"🔧 تهيئة مدير البيانات...","timestamp":"2025-07-11 19:48:08"}
{"level":"info","message":"📝 لا توجد بيانات رصيد محفوظة، بدء جديد","timestamp":"2025-07-11 19:48:08"}
{"level":"info","message":"✅ تم تهيئة مدير الرصيد بنجاح","timestamp":"2025-07-11 19:48:08"}
{"level":"info","message":"📥 تم تحميل 11 أداة مالية محفوظة","timestamp":"2025-07-11 19:48:08"}
{"level":"info","message":"📊 جلب الأدوات المالية من المنصة...","timestamp":"2025-07-11 19:48:08"}
{"level":"info","message":"✅ تم جلب 11 أداة مالية","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"✅ تم تهيئة مدير الأدوات المالية بنجاح","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"📥 تم تحميل البيانات التاريخية لـ 0 رمز","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات التاريخية بنجاح","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات الحية بنجاح","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات بنجاح","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"📝 لا توجد بيانات صفقات محفوظة، بدء جديد","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"✅ تم تهيئة مدير التداول بنجاح","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"🧠 تهيئة مدير الاستراتيجيات...","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"📊 تم تحميل 3 استراتيجية","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"📝 لا توجد إشارات محفوظة، بدء جديد","timestamp":"2025-07-11 19:48:13"}
{"level":"info","message":"🚀 بدء تشغيل روبوت التداول Quotex","timestamp":"2025-07-11 19:49:46"}
{"level":"info","message":"🚀 بدء تشغيل روبوت التداول...","timestamp":"2025-07-11 19:49:46"}
{"level":"info","message":"✅ تم التحقق من الإعدادات بنجاح","timestamp":"2025-07-11 19:49:46"}
{"level":"info","message":"🔗 بدء الاتصال بمنصة Quotex...","timestamp":"2025-07-11 19:49:46","type":"connection"}
{"level":"info","message":"🔗 تم إطلاق المتصفح بنجاح","timestamp":"2025-07-11 19:49:46","type":"connection"}
{"level":"info","message":"🔗 تم إنشاء صفحة جديدة","timestamp":"2025-07-11 19:49:46","type":"connection"}
{"level":"info","message":"🔗 تم إعداد مراقبة الشبكة","timestamp":"2025-07-11 19:49:46","type":"connection"}
{"level":"info","message":"🔗 الانتقال إلى: https://qxbroker.com/en/demo-trade","timestamp":"2025-07-11 19:49:46","type":"connection"}
{"level":"info","message":"🔗 تم الانتقال إلى المنصة","timestamp":"2025-07-11 19:49:52","type":"connection"}
{"level":"warn","message":"تحذير: قد لا تكون المنصة محملة بالكامل:","timestamp":"2025-07-11 19:50:22"}
{"level":"info","message":"🔗 تم استخراج بيانات الجلسة","sessionId":"غير موجود","timestamp":"2025-07-11 19:50:22","type":"connection"}
{"level":"info","message":"🔗 تم الاتصال بمنصة Quotex بنجاح","timestamp":"2025-07-11 19:50:22","type":"connection"}
{"level":"info","message":"🔗 تم الاتصال بمنصة Quotex بنجاح","timestamp":"2025-07-11 19:50:22","type":"connection"}
{"level":"info","message":"🔧 تهيئة مدير البيانات...","timestamp":"2025-07-11 19:50:22"}
{"level":"info","message":"📝 لا توجد بيانات رصيد محفوظة، بدء جديد","timestamp":"2025-07-11 19:50:22"}
{"level":"info","message":"✅ تم تهيئة مدير الرصيد بنجاح","timestamp":"2025-07-11 19:50:22"}
{"level":"info","message":"📥 تم تحميل 11 أداة مالية محفوظة","timestamp":"2025-07-11 19:50:22"}
{"level":"info","message":"📊 جلب الأدوات المالية من المنصة...","timestamp":"2025-07-11 19:50:22"}
{"level":"info","message":"✅ تم جلب 11 أداة مالية","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"✅ تم تهيئة مدير الأدوات المالية بنجاح","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📥 تم تحميل البيانات التاريخية لـ 0 رمز","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات التاريخية بنجاح","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات الحية بنجاح","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"✅ تم تهيئة مدير البيانات بنجاح","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📝 لا توجد بيانات صفقات محفوظة، بدء جديد","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"✅ تم تهيئة مدير التداول بنجاح","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"🧠 تهيئة مدير الاستراتيجيات...","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📊 تم تحميل 3 استراتيجية","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📝 لا توجد إشارات محفوظة، بدء جديد","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📊 تم تفعيل الاستراتيجية: Simple Test Strategy","timestamp":"2025-07-11 19:50:27","type":"strategy"}
{"level":"info","message":"✅ تم تهيئة مدير الاستراتيجيات بنجاح","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📊 بدء جمع البيانات...","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📥 تحميل البيانات الأولية...","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"💰 الرصيد الحالي: $null","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📋 تم جلب 11 أداة مالية","timestamp":"2025-07-11 19:50:27"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ ADAUSD...","timestamp":"2025-07-11 19:50:27"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ ADAUSD","timestamp":"2025-07-11 19:50:30"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ AUDUSD...","timestamp":"2025-07-11 19:50:30"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ AUDUSD","timestamp":"2025-07-11 19:50:33"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ BTCUSD...","timestamp":"2025-07-11 19:50:33"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ BTCUSD","timestamp":"2025-07-11 19:50:36"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ DOGUSD...","timestamp":"2025-07-11 19:50:36"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ DOGUSD","timestamp":"2025-07-11 19:50:39"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ ETHUSD...","timestamp":"2025-07-11 19:50:39"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ ETHUSD","timestamp":"2025-07-11 19:50:42"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ EURUSD...","timestamp":"2025-07-11 19:50:42"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ EURUSD","timestamp":"2025-07-11 19:50:45"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ GBPUSD...","timestamp":"2025-07-11 19:50:45"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ GBPUSD","timestamp":"2025-07-11 19:50:48"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ LTCUSD...","timestamp":"2025-07-11 19:50:48"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ LTCUSD","timestamp":"2025-07-11 19:50:51"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ USDCAD...","timestamp":"2025-07-11 19:50:51"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ USDCAD","timestamp":"2025-07-11 19:50:54"}
{"level":"info","message":"📊 جلب البيانات التاريخية لـ USDCHF...","timestamp":"2025-07-11 19:50:54"}
{"level":"warn","message":"⚠️ تم إنشاء بيانات وهمية لـ USDCHF","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"✅ تم تحميل البيانات الأولية بنجاح","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"👁️ بدء مراقبة البيانات الحية...","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"✅ تم بدء مراقبة البيانات الحية بنجاح","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"👁️ بدء مراقبة الرصيد...","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"✅ تم بدء مراقبة الرصيد بنجاح","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"✅ تم بدء جمع البيانات بنجاح","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"✅ تم تشغيل الروبوت بنجاح","timestamp":"2025-07-11 19:50:57"}
{"level":"info","message":"💰 تحديث الرصيد: $5","timestamp":"2025-07-11 19:51:02"}
{"level":"info","message":"💰 تحديث الرصيد: $5","timestamp":"2025-07-11 19:51:02"}
{"level":"warn","message":"⚠️ تم الوصول لحد الخسارة، إيقاف التداول","timestamp":"2025-07-11 19:51:02"}
{"level":"info","message":"📊 تم إيقاف الاستراتيجية مؤقتاً","timestamp":"2025-07-11 19:51:02","type":"strategy"}
{"level":"warn","message":"⚠️ تم الوصول لحد الخسارة، إيقاف التداول","timestamp":"2025-07-11 19:51:07"}
{"level":"info","message":"📊 تم إيقاف الاستراتيجية مؤقتاً","timestamp":"2025-07-11 19:51:07","type":"strategy"}
{"level":"warn","message":"⚠️ تم الوصول لحد الخسارة، إيقاف التداول","timestamp":"2025-07-11 19:51:12"}
{"level":"info","message":"📊 تم إيقاف الاستراتيجية مؤقتاً","timestamp":"2025-07-11 19:51:12","type":"strategy"}
{"level":"warn","message":"⚠️ تم الوصول لحد الخسارة، إيقاف التداول","timestamp":"2025-07-11 19:51:17"}
{"level":"info","message":"📊 تم إيقاف الاستراتيجية مؤقتاً","timestamp":"2025-07-11 19:51:17","type":"strategy"}
{"cause":{"name":"ProtocolError"},"level":"error","message":"فشل في جلب الرصيد: Protocol error (Runtime.callFunctionOn): Target closed","name":"TargetCloseError","stack":"TargetCloseError: Protocol error (Runtime.callFunctionOn): Target closed\n    at CallbackRegistry.clear (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\common\\CallbackRegistry.js:75:36)\n    at CdpCDPSession._onClosed (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\CDPSession.js:101:25)\n    at #onClose (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\Connection.js:157:21)\n    at WebSocket.<anonymous> (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\node\\NodeWebSocketTransport.js:47:30)\n    at callListener (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\ws\\lib\\event-target.js:290:14)\n    at WebSocket.onClose (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\ws\\lib\\event-target.js:220:9)\n    at WebSocket.emit (node:events:518:28)\n    at WebSocket.emitClose (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\ws\\lib\\websocket.js:265:10)\n    at Receiver.receiverOnFinish (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\ws\\lib\\websocket.js:1198:20)\n    at Receiver.emit (node:events:518:28)","timestamp":"2025-07-11 19:51:21"}
{"level":"error","message":"فشل في جلب الرصيد: Requesting main frame too early!","stack":"Error: Requesting main frame too early!\n    at assert (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\util\\assert.js:18:15)\n    at FrameManager.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\FrameManager.js:213:32)\n    at CdpPage.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\Page.js:406:35)\n    at CdpPage.evaluate (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:841:31)\n    at QuotexConnection.evaluate (C:\\Users\\<USER>\\Desktop\\new\\src\\connection\\QuotexConnection.js:318:32)\n    at BalanceManager.fetchBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:180:51)\n    at BalanceManager.updateBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:139:43)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:93:32)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-11 19:51:21"}
{"level":"warn","message":"⚠️ تم الوصول لحد الخسارة، إيقاف التداول","timestamp":"2025-07-11 19:51:22"}
{"level":"info","message":"📊 تم إيقاف الاستراتيجية مؤقتاً","timestamp":"2025-07-11 19:51:22","type":"strategy"}
{"level":"error","message":"فشل في جلب الرصيد: Requesting main frame too early!","stack":"Error: Requesting main frame too early!\n    at assert (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\util\\assert.js:18:15)\n    at FrameManager.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\FrameManager.js:213:32)\n    at CdpPage.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\Page.js:406:35)\n    at CdpPage.evaluate (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:841:31)\n    at QuotexConnection.evaluate (C:\\Users\\<USER>\\Desktop\\new\\src\\connection\\QuotexConnection.js:318:32)\n    at BalanceManager.fetchBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:180:51)\n    at BalanceManager.updateBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:139:43)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:93:32)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-11 19:51:23"}
{"level":"error","message":"فشل في جلب الرصيد: Requesting main frame too early!","stack":"Error: Requesting main frame too early!\n    at assert (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\util\\assert.js:18:15)\n    at FrameManager.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\FrameManager.js:213:32)\n    at CdpPage.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\Page.js:406:35)\n    at CdpPage.evaluate (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:841:31)\n    at QuotexConnection.evaluate (C:\\Users\\<USER>\\Desktop\\new\\src\\connection\\QuotexConnection.js:318:32)\n    at BalanceManager.fetchBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:180:51)\n    at BalanceManager.updateBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:139:43)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:93:32)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-11 19:51:24"}
{"level":"error","message":"فشل في جلب الرصيد: Requesting main frame too early!","stack":"Error: Requesting main frame too early!\n    at assert (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\util\\assert.js:18:15)\n    at FrameManager.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\FrameManager.js:213:32)\n    at CdpPage.mainFrame (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\cdp\\Page.js:406:35)\n    at CdpPage.evaluate (C:\\Users\\<USER>\\Desktop\\new\\node_modules\\puppeteer-core\\lib\\cjs\\puppeteer\\api\\Page.js:841:31)\n    at QuotexConnection.evaluate (C:\\Users\\<USER>\\Desktop\\new\\src\\connection\\QuotexConnection.js:318:32)\n    at BalanceManager.fetchBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:180:51)\n    at BalanceManager.updateBalance (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:139:43)\n    at Timeout._onTimeout (C:\\Users\\<USER>\\Desktop\\new\\src\\data\\BalanceManager.js:93:32)\n    at listOnTimeout (node:internal/timers:588:17)\n    at process.processTimers (node:internal/timers:523:7)","timestamp":"2025-07-11 19:51:25"}
