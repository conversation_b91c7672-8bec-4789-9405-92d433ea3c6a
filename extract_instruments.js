const fs = require('fs');

// قراءة ملف الرسائل
const messages = JSON.parse(fs.readFileSync('ws_messages.json', 'utf8'));

console.log('استخراج وتحليل بيانات الأدوات المالية...');

// البحث عن الرسائل التي تحتوي على بيانات الأدوات المالية
const instrumentsMessages = [];
const priceUpdateMessages = [];

for (let i = 0; i < messages.length; i++) {
    const msg = messages[i];
    
    // البحث عن الرسائل التي تحتوي على أسماء العملات المعروفة
    if (msg.message.includes('EURUSD') || msg.message.includes('BTCUSD') || 
        msg.message.includes('ADAUSD') || msg.message.includes('currency') ||
        msg.message.includes('cryptocurrency') || msg.message.includes('stock')) {
        
        // هذه رسالة تحتوي على بيانات الأدوات المالية
        instrumentsMessages.push({
            timestamp: msg.timestamp,
            direction: msg.type,
            messageLength: msg.message.length,
            preview: msg.message.substring(0, 500)
        });
        
        // محاولة تحليل البيانات
        try {
            // إزالة أي أحرف غير مرغوب فيها من بداية الرسالة
            let cleanMessage = msg.message;
            if (cleanMessage.startsWith('BFtb')) {
                // هذه رسالة مشفرة بـ Base64، نحتاج لفك تشفيرها
                const decoded = Buffer.from(cleanMessage, 'base64').toString('utf8');
                
                // محاولة تحليل البيانات كـ JSON
                if (decoded.startsWith('[[')) {
                    const instrumentsData = JSON.parse(decoded);
                    
                    // تحليل كل أداة مالية
                    const parsedInstruments = instrumentsData.map(inst => {
                        if (Array.isArray(inst) && inst.length > 20) {
                            return {
                                id: inst[0],
                                symbol: inst[1],
                                name: inst[2],
                                type: inst[3], // currency, cryptocurrency, stock
                                precision: inst[4],
                                percentage: inst[5],
                                timeframe: inst[6],
                                minTime: inst[7],
                                maxTime: inst[8],
                                isOTC: inst[9] === 1,
                                groupId: inst[10],
                                parentId: inst[11],
                                tradingOptions: inst[12], // call/put options
                                timestamp: inst[13],
                                isActive: inst[14],
                                priceData: inst[15],
                                currentPrice: inst[16],
                                priceChange: inst[17],
                                high: inst[18],
                                low: inst[19],
                                volume: inst[20],
                                bid: inst[21],
                                ask: inst[22],
                                spread: inst[23],
                                dailyChange: inst[24],
                                weeklyChange: inst[25],
                                monthlyChange: inst[26]
                            };
                        }
                        return null;
                    }).filter(inst => inst !== null);
                    
                    if (parsedInstruments.length > 0) {
                        instrumentsMessages[instrumentsMessages.length - 1].parsedInstruments = parsedInstruments;
                        instrumentsMessages[instrumentsMessages.length - 1].instrumentsCount = parsedInstruments.length;
                    }
                }
            }
        } catch (e) {
            // في حالة فشل التحليل، نحتفظ بالرسالة كما هي
        }
    }
    
    // البحث عن رسائل تحديث الأسعار
    if (msg.message.includes('quotes/stream') || msg.message.includes('price')) {
        priceUpdateMessages.push({
            timestamp: msg.timestamp,
            direction: msg.type,
            messageType: 'price_update'
        });
    }
}

console.log(`\n=== نتائج الاستخراج ===`);
console.log(`رسائل الأدوات المالية: ${instrumentsMessages.length}`);
console.log(`رسائل تحديث الأسعار: ${priceUpdateMessages.length}`);

// استخراج جميع الأدوات المالية الفريدة
const allInstruments = [];
const instrumentsMap = new Map();

instrumentsMessages.forEach(msg => {
    if (msg.parsedInstruments) {
        msg.parsedInstruments.forEach(inst => {
            if (!instrumentsMap.has(inst.symbol)) {
                instrumentsMap.set(inst.symbol, inst);
                allInstruments.push(inst);
            }
        });
    }
});

console.log(`إجمالي الأدوات المالية المكتشفة: ${allInstruments.length}`);

// تصنيف الأدوات حسب النوع
const currencies = allInstruments.filter(inst => inst.type === 'currency');
const cryptocurrencies = allInstruments.filter(inst => inst.type === 'cryptocurrency');
const stocks = allInstruments.filter(inst => inst.type === 'stock');

console.log(`العملات التقليدية: ${currencies.length}`);
console.log(`العملات المشفرة: ${cryptocurrencies.length}`);
console.log(`الأسهم: ${stocks.length}`);

// إنشاء قائمة الرسائل المهمة لروبوت التداول
const tradingBotGuide = {
    "دليل الرسائل المهمة لروبوت التداول": {
        "1. رسائل الاتصال": {
            "نوع": "Socket.IO Connection",
            "أهمية": "عالية جداً",
            "وصف": "رسائل تأسيس الاتصال مع خادم Quotex",
            "مثال": "0{\"sid\":\"wzpsexUJgNs37vTFDbn2\",\"upgrades\":[],\"pingInterval\":25000,\"pingTimeout\":5000}"
        },
        "2. رسائل التفويض": {
            "نوع": "Authorization",
            "أهمية": "عالية جداً", 
            "وصف": "رسائل تسجيل الدخول والتفويض للوصول للحساب",
            "مثال": "42[\"authorization\",{\"session\":\"SESSION_ID\",\"isDemo\":0,\"tournamentId\":0}]"
        },
        "3. رسائل قوائم الأدوات المالية": {
            "نوع": "Instruments List",
            "أهمية": "عالية",
            "وصف": "قوائم العملات والأسهم والعملات المشفرة المتاحة للتداول",
            "تشفير": "Base64",
            "عدد_الرسائل": instrumentsMessages.length,
            "إجمالي_الأدوات": allInstruments.length
        },
        "4. رسائل تحديث الأسعار": {
            "نوع": "Price Updates",
            "أهمية": "عالية جداً",
            "وصف": "تحديثات الأسعار في الوقت الفعلي",
            "مثال": "451-[\"quotes/stream\",{\"_placeholder\":true,\"num\":0}]",
            "عدد_الرسائل": priceUpdateMessages.length
        },
        "5. رسائل وضع الصفقات": {
            "نوع": "Trade Execution",
            "أهمية": "عالية جداً",
            "وصف": "رسائل وضع صفقات Call/Put",
            "ملاحظة": "لم يتم العثور عليها في هذه الجلسة - تحتاج لتسجيل جلسة تداول فعلية"
        }
    },
    "الأدوات_المالية_المتاحة": {
        "العملات_التقليدية": currencies.slice(0, 20).map(c => ({
            symbol: c.symbol,
            name: c.name,
            isOTC: c.isOTC,
            isActive: c.isActive
        })),
        "العملات_المشفرة": cryptocurrencies.slice(0, 20).map(c => ({
            symbol: c.symbol,
            name: c.name,
            isOTC: c.isOTC,
            isActive: c.isActive
        })),
        "الأسهم": stocks.slice(0, 20).map(s => ({
            symbol: s.symbol,
            name: s.name,
            isOTC: s.isOTC,
            isActive: s.isActive
        }))
    }
};

// حفظ النتائج
fs.writeFileSync('trading_bot_complete_guide.json', JSON.stringify({
    summary: {
        totalMessages: messages.length,
        instrumentsMessages: instrumentsMessages.length,
        priceUpdateMessages: priceUpdateMessages.length,
        totalInstruments: allInstruments.length,
        currencies: currencies.length,
        cryptocurrencies: cryptocurrencies.length,
        stocks: stocks.length
    },
    tradingBotGuide: tradingBotGuide,
    allInstruments: allInstruments,
    sampleMessages: {
        instrumentsMessages: instrumentsMessages.slice(0, 3),
        priceUpdateMessages: priceUpdateMessages.slice(0, 10)
    }
}, null, 2));

console.log('\nتم حفظ الدليل الكامل في ملف trading_bot_complete_guide.json');
console.log('\n=== أهم الرسائل لروبوت التداول ===');
console.log('1. رسائل الاتصال (Socket.IO Connection)');
console.log('2. رسائل التفويض (Authorization)'); 
console.log('3. رسائل قوائم الأدوات المالية (Instruments List)');
console.log('4. رسائل تحديث الأسعار (Price Updates)');
console.log('5. رسائل وضع الصفقات (Trade Execution) - غير موجودة في هذه الجلسة');
